import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { LibraryService } from '../../../../core/services/library.service';
import { CartService } from '../../../../core/services/cart.service';
import { ModalService } from '../../../../core/services/modal.service';
import { LibraryItem, LibraryFilters } from '../../../../core/models/library.model';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-user-library',
  standalone: false,
  templateUrl: './user-library.component.html',
  styleUrl: './user-library.component.css'
})
export class UserLibraryComponent implements OnInit, OnDestroy {
  libraryItems: LibraryItem[] = [];
  loading = false;
  error = '';

  // Pagination
  totalItems = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-added_at';
  private searchSubject = new Subject<string>();
  private searchSubscription?: Subscription;

  constructor(
    private libraryService: LibraryService,
    private cartService: CartService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadLibrary();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.searchSubscription = this.searchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadLibrary();
    });
  }

  loadLibrary(): void {
    this.loading = true;
    this.error = '';

    const filters: LibraryFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.libraryService.getLibrary(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.libraryItems = response.results;
        this.totalItems = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить библиотеку';
        this.loading = false;
        this.modalService.error('Ошибка', this.error);
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadLibrary();
  }

  goToGameDetail(gameId: number): void {
    this.router.navigate(['/profile/library/games', gameId]);
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.loadLibrary();
    }
  }

  nextPage(): void {
    if (this.hasNext) {
      this.goToPage(this.currentPage + 1);
    }
  }

  previousPage(): void {
    if (this.hasPrevious) {
      this.goToPage(this.currentPage - 1);
    }
  }

  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  hasActiveAccess(game: any): boolean {
    return game.has_access || false;
  }

  getAccessEndDate(game: any): string {
    if (!game.access_end) return '';
    const accessEnd = new Date(game.access_end);
    return accessEnd.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getRemainingTime(game: any): string {
    if (!game.access_end) return '';
    const accessEnd = new Date(game.access_end);
    const now = new Date();
    const diff = accessEnd.getTime() - now.getTime();

    if (diff <= 0) return 'Доступ истёк';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days} ${days === 1 ? 'день' : days < 5 ? 'дня' : 'дней'}`;
    } else if (hours > 0) {
      return `${hours} ${hours === 1 ? 'час' : hours < 5 ? 'часа' : 'часов'}`;
    } else {
      return `${minutes} ${minutes === 1 ? 'минута' : minutes < 5 ? 'минуты' : 'минут'}`;
    }
  }

  extendAccess(game: Game): void {
    // Check if game is already in cart
    if (this.cartService.isInCart(game.id)) {
      this.modalService.error('Игра в корзине', 'Эта игра уже добавлена в корзину. Перейдите в корзину для оформления покупки.');
      return;
    }

    // Add game to cart for access extension
    this.cartService.addToCart(game).subscribe({
      next: () => {
        this.modalService.success(
          'Добавлено в корзину',
          `Игра "${game.title}" добавлена в корзину для продления доступа. Перейдите в корзину для оформления покупки.`
        );
      },
      error: (error) => {
        console.error('Error adding game to cart:', error);
        this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
      }
    });
  }

  needsAccessExtension(game: Game): boolean {
    return (game.is_in_library || false) && !(game.has_access || false);
  }
}

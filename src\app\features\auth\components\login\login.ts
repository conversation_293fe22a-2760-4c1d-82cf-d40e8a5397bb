import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../../core/services/auth.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { ModalService } from '../../../../core/services/modal.service';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.html',
  styleUrl: './login.css'
})
export class Login implements OnInit, OnDestroy {
  loginForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  private loadingSubscription?: Subscription;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private loadingService: LoadingService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.initializeForm();

    // Subscribe to login loading state
    this.loadingSubscription = this.loadingService.getLoading('login').subscribe(
      loading => this.isLoading = loading
    );

    // Redirect if already authenticated
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/']);
    }
  }

  ngOnDestroy(): void {
    this.loadingSubscription?.unsubscribe();
  }

  private initializeForm(): void {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid && !this.isLoading) {
      this.errorMessage = '';

      const credentials = this.loginForm.value;

      this.authService.login(credentials).subscribe({
        next: (response) => {
          // Show success message with modal
          this.modalService.success('Успешный вход', 'Добро пожаловать!').then(() => {
            this.router.navigate(['/']); // Redirect to main page after successful login
          });
        },
        error: (error) => {
          // Show error in modal instead of inline message
          this.modalService.error('Ошибка входа', error.message || 'Не удалось войти в систему. Попробуйте снова.');
        }
      });
    } else {
      // Show form validation errors in modal
      this.showFormValidationErrors();
    }
  }

  private showFormValidationErrors(): void {
    this.markFormGroupTouched();
    const errors: string[] = [];

    Object.keys(this.loginForm.controls).forEach(key => {
      const fieldError = this.getFieldError(key);
      if (fieldError) {
        errors.push(fieldError);
      }
    });

    if (errors.length > 0) {
      this.modalService.error('Ошибки в форме', errors.join('\n'));
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);

    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName === 'email' ? 'Email' : 'Password'} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return 'Password must be at least 6 characters long';
      }
    }

    return '';
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field?.invalid && field.touched);
  }
}

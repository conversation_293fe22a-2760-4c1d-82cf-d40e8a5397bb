<!-- Game Keys Management Component -->
<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <div>
      <h2 class="text-2xl font-bold text-white mb-2">Управление ключами игр</h2>
      <p class="text-gray-400">Управление ключами активации для игр</p>
    </div>
    <button 
      (click)="toggleAddForm()"
      class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      Добавить ключ
    </button>
  </div>

  <!-- Add Game Key Form -->
  <div *ngIf="showAddForm" class="bg-slate-800/50 border border-slate-600/30 rounded-lg p-6 mb-6">
    <h3 class="text-lg font-semibold text-white mb-4">Добавить новый ключ игры</h3>
    
    <form (ngSubmit)="onAddGameKey()" class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Game Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Игра *</label>
          <select 
            [(ngModel)]="newGameKey.game" 
            name="game"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="0">Выберите игру</option>
            <option *ngFor="let game of games" [value]="game.id">{{ game.title }}</option>
          </select>
        </div>

        <!-- Game Key Code -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Код ключа *</label>
          <input 
            type="text" 
            [(ngModel)]="newGameKey.code" 
            name="code"
            placeholder="Введите код ключа"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
      </div>

      <!-- Expiration Date (Optional) -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Дата истечения (необязательно)</label>
        <input 
          type="datetime-local" 
          [(ngModel)]="newGameKey.expires_at" 
          name="expires_at"
          class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
      </div>

      <!-- Error Message -->
      <div *ngIf="addGameKeyError" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
        <p class="text-red-300 text-sm">{{ addGameKeyError }}</p>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3">
        <button 
          type="button" 
          (click)="toggleAddForm()"
          class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
          Отмена
        </button>
        <button 
          type="submit" 
          [disabled]="addGameKeyLoading"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center">
          <svg *ngIf="addGameKeyLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ addGameKeyLoading ? 'Добавление...' : 'Добавить ключ' }}
        </button>
      </div>
    </form>
  </div>

  <!-- Filters and Search -->
  <div class="bg-slate-800/50 border border-slate-600/30 rounded-lg p-4">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Поиск</label>
        <input 
          type="text" 
          [(ngModel)]="searchTerm" 
          (input)="onSearchChange()"
          placeholder="Поиск по коду или игре..."
          class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
      </div>

      <!-- Game Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Игра</label>
        <select 
          [(ngModel)]="selectedGame" 
          (change)="onGameFilterChange()"
          class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <option [value]="null">Все игры</option>
          <option *ngFor="let game of games" [value]="game.id">{{ game.title }}</option>
        </select>
      </div>

      <!-- Usage Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Статус использования</label>
        <select 
          [(ngModel)]="selectedUsageStatus" 
          (change)="onUsageStatusFilterChange()"
          class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <option value="all">Все</option>
          <option value="unused">Не использованы</option>
          <option value="used">Использованы</option>
        </select>
      </div>

      <!-- Sort -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Сортировка</label>
        <select 
          [(ngModel)]="sortBy" 
          (change)="onSortChange()"
          class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <option value="-id">Новые первыми</option>
          <option value="id">Старые первыми</option>
          <option value="game_title">По названию игры (А-Я)</option>
          <option value="-game_title">По названию игры (Я-А)</option>
          <option value="code">По коду (А-Я)</option>
          <option value="-code">По коду (Я-А)</option>
          <option value="is_used">Неиспользованные первыми</option>
          <option value="-is_used">Использованные первыми</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="gameKeysLoading" class="flex justify-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="gameKeysError && !gameKeysLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
    <p class="text-red-300">{{ gameKeysError }}</p>
    <button (click)="loadGameKeys()" class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
      Попробовать снова
    </button>
  </div>

  <!-- Game Keys Table -->
  <div *ngIf="!gameKeysLoading && !gameKeysError" class="bg-slate-800/50 border border-slate-600/30 rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-slate-700/50">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Игра</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Код</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Статус</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Назначен</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Истекает</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Действия</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-slate-600/30">
          <tr *ngFor="let gameKey of gameKeys" class="hover:bg-slate-700/30 transition-colors">
            <td class="px-4 py-3 text-sm text-gray-300">{{ gameKey.id }}</td>
            <td class="px-4 py-3 text-sm text-white">{{ gameKey.game_title }}</td>
            <td class="px-4 py-3 text-sm font-mono text-blue-300">{{ gameKey.code }}</td>
            <td class="px-4 py-3 text-sm">
              <span [ngClass]="getUsageStatusClass(gameKey.is_used)" class="font-medium">
                {{ getUsageStatusText(gameKey.is_used) }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-gray-300">
              {{ gameKey.assigned_to_user ? 'Пользователь #' + gameKey.assigned_to_user : 'Не назначен' }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-300">{{ formatDate(gameKey.expires_at) }}</td>
            <td class="px-4 py-3 text-sm">
              <button 
                (click)="deleteGameKey(gameKey)"
                class="text-red-400 hover:text-red-300 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="gameKeys.length === 0" class="text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-300">Ключи не найдены</h3>
      <p class="mt-1 text-sm text-gray-400">Начните с добавления нового ключа игры.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages > 1" class="flex justify-between items-center">
    <div class="text-sm text-gray-400">
      Показано {{ gameKeys.length }} из {{ totalGameKeys }} ключей
    </div>
    <div class="flex space-x-1">
      <button 
        (click)="onPageChange(currentPage - 1)" 
        [disabled]="!hasPrevious"
        class="px-3 py-1 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-white rounded text-sm transition-colors">
        Назад
      </button>
      <button 
        *ngFor="let page of pages" 
        (click)="onPageChange(page)"
        [ngClass]="{'bg-blue-600': page === currentPage, 'bg-slate-700 hover:bg-slate-600': page !== currentPage}"
        class="px-3 py-1 text-white rounded text-sm transition-colors">
        {{ page }}
      </button>
      <button 
        (click)="onPageChange(currentPage + 1)" 
        [disabled]="!hasNext"
        class="px-3 py-1 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-white rounded text-sm transition-colors">
        Вперед
      </button>
    </div>
  </div>
</div>

import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GameService } from '../../core/services/game.service';
import { CartService } from '../../core/services/cart.service';
import { CheckoutService } from '../../core/services/checkout.service';
import { PurchaseService } from '../../core/services/purchase.service';
import { UserSummaryService } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { Game } from '../../core/models/game.model';
import { AccessTypeSelection } from '../../core/models/cart.model';

@Component({
  selector: 'app-game-detail',
  standalone: false,
  templateUrl: './game-detail.component.html',
  styleUrl: './game-detail.component.css'
})
export class GameDetailComponent implements OnInit, OnDestroy {
  game: Game | null = null;
  loading = false;
  error = '';
  gameId: number | null = null;

  // Gallery
  showGalleryModal = false;
  currentImageIndex = 0;
  galleryImages: string[] = [];
  private keydownListener?: (event: KeyboardEvent) => void;

  // Cart
  cartItemCount = 0;
  private cartSubscription?: Subscription;
  private cartChangesSubscription?: Subscription;

  // Renewal functionality
  showAccessTypeModal = false;
  accessTypeSelection: AccessTypeSelection | null = null;
  renewalLoading = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gameService: GameService,
    private cartService: CartService,
    private checkoutService: CheckoutService,
    private purchaseService: PurchaseService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.gameId = +params['id'];
      if (this.gameId) {
        this.loadGame();
      }
    });

    this.setupCartSubscription();
    this.setupCartChangesSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
    this.cartChangesSubscription?.unsubscribe();

    // Clean up gallery modal if open
    if (this.showGalleryModal) {
      this.closeGallery();
    }
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cartItemCount = cart.total_items;
    });
  }

  private setupCartChangesSubscription(): void {
    this.cartChangesSubscription = this.cartService.cartChanges$.subscribe(change => {
      // Update the current game's is_in_cart status if it matches
      if (this.game && this.game.id === change.gameId) {
        this.game.is_in_cart = change.action === 'added';
      }
    });
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.setupGallery();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить информацию об игре';
        this.loading = false;
      }
    });
  }

  private setupGallery(): void {
    if (this.game?.gallery_items) {
      this.galleryImages = this.game.gallery_items.map(item => item.file);
    }
  }

  openGallery(index: number): void {
    this.currentImageIndex = index;
    this.showGalleryModal = true;
    document.body.style.overflow = 'hidden';

    // Add keyboard event listener
    this.keydownListener = (event: KeyboardEvent) => this.handleKeyPress(event);
    document.addEventListener('keydown', this.keydownListener);
  }

  closeGallery(): void {
    this.showGalleryModal = false;
    document.body.style.overflow = 'auto';

    // Remove keyboard event listener
    if (this.keydownListener) {
      document.removeEventListener('keydown', this.keydownListener);
      this.keydownListener = undefined;
    }
  }

  private handleKeyPress(event: KeyboardEvent): void {
    if (!this.showGalleryModal) return;

    switch (event.key) {
      case 'Escape':
        this.closeGallery();
        break;
      case 'ArrowLeft':
        this.previousImage();
        break;
      case 'ArrowRight':
        this.nextImage();
        break;
    }
  }

  nextImage(): void {
    if (this.currentImageIndex < this.galleryImages.length - 1) {
      this.currentImageIndex++;
    } else {
      this.currentImageIndex = 0; // Loop to first image
    }
  }

  previousImage(): void {
    if (this.currentImageIndex > 0) {
      this.currentImageIndex--;
    } else {
      this.currentImageIndex = this.galleryImages.length - 1; // Loop to last image
    }
  }

  goToImage(index: number): void {
    this.currentImageIndex = index;
  }

  addToCart(): void {
    if (this.game) {
      // Check if user has active access - if so, they don't need to buy again
      if (this.canPlay()) {
        this.modalService.error('Игра доступна', 'У вас уже есть активный доступ к этой игре');
        return;
      }

      this.cartService.addToCart(this.game).subscribe({
        next: () => {
          // Success - cart will be automatically updated via subscription
          // Update the local game object to reflect the cart status immediately
          if (this.game) {
            this.game.is_in_cart = true;
          }

          const actionText = this.needsAccessExtension() ? 'для продления доступа' : '';
          this.modalService.success('Успех', `Игра добавлена в корзину ${actionText}`);
          console.log('Game added to cart successfully');
        },
        error: (error) => {
          console.error('Error adding game to cart:', error.message);
          this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
        }
      });
    }
  }

  isInCart(): boolean {
    if (!this.game) return false;

    // Use the is_in_cart field from the API response if available (when user is authenticated)
    // Fall back to cart service check if not available (when user is not authenticated)
    return this.game.is_in_cart !== undefined ? this.game.is_in_cart : this.cartService.isInCart(this.game.id);
  }

  isInLibrary(): boolean {
    if (!this.game) return false;
    return this.game.is_in_library || false;
  }

  hasActiveAccess(): boolean {
    if (!this.game) return false;
    return this.game.has_access || false;
  }

  canPlay(): boolean {
    return this.isInLibrary() && this.hasActiveAccess();
  }

  needsAccessExtension(): boolean {
    return this.isInLibrary() && !this.hasActiveAccess();
  }

  canBuy(): boolean {
    return !this.isInLibrary();
  }

  goBack(): void {
    this.router.navigate(['/games']);
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  // Legacy method - now using API's has_access field instead
  hasActiveAccessByDate(): boolean {
    if (!this.game || !this.game.access_end) return false;
    const accessEnd = new Date(this.game.access_end);
    return accessEnd > new Date();
  }

  getAccessEndDate(): string {
    if (!this.game?.access_end) return '';
    const accessEnd = new Date(this.game.access_end);
    return accessEnd.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getRemainingTime(): string {
    if (!this.game?.access_end) return '';
    const accessEnd = new Date(this.game.access_end);
    const now = new Date();
    const diff = accessEnd.getTime() - now.getTime();

    if (diff <= 0) return 'Доступ истёк';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days} ${days === 1 ? 'день' : days < 5 ? 'дня' : 'дней'}`;
    } else if (hours > 0) {
      return `${hours} ${hours === 1 ? 'час' : hours < 5 ? 'часа' : 'часов'}`;
    } else {
      return `${minutes} ${minutes === 1 ? 'минута' : minutes < 5 ? 'минуты' : 'минут'}`;
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  renewAccess(): void {
    if (!this.game) return;

    // For renewal, we can directly add to cart since the API handles duplicates
    // Show access type selection modal for renewal
    this.accessTypeSelection = {
      purchaseId: 0, // Will be set after creating purchase
      gameTitle: this.game.title,
      price: this.game.price
    };
    this.showAccessTypeModal = true;
  }

  extendAccess(): void {
    // This is the same as renewAccess - just add to cart for access extension
    this.addToCart();
  }

  onAccessTypeSelected(accessType: 'oneday' | 'subscription'): void {
    if (!this.game) return;

    this.renewalLoading = true;

    // First, add the game to cart
    this.cartService.addToCart(this.game).subscribe({
      next: () => {
        // Then checkout to create a purchase
        this.checkoutService.checkout().subscribe({
          next: (purchases) => {
            if (purchases.length > 0) {
              const purchase = purchases[0]; // Get the first (and should be only) purchase

              // Now pay for the purchase with the selected access type
              this.purchaseService.payForPurchase(purchase.id, accessType).subscribe({
                next: (response) => {
                  this.renewalLoading = false;

                  // Refresh the game data to get updated access information
                  this.loadGame();

                  // Refresh user summary
                  this.userSummaryService.refreshSummary();

                  const accessTypeText = accessType === 'oneday' ? 'на 1 день' : 'на 30 дней';
                  this.modalService.success(
                    'Доступ продлён',
                    `${response.detail || 'Доступ успешно продлён'} Новый доступ предоставлен ${accessTypeText}.`
                  );
                },
                error: (error) => {
                  this.renewalLoading = false;
                  console.error('Renewal payment error:', error.message);
                  this.modalService.error('Ошибка продления', error.message);
                }
              });
            } else {
              this.renewalLoading = false;
              this.modalService.error('Ошибка', 'Не удалось создать покупку для продления');
            }
          },
          error: (error) => {
            this.renewalLoading = false;
            console.error('Checkout error:', error.message);
            this.modalService.error('Ошибка оформления', error.message);
          }
        });
      },
      error: (error) => {
        this.renewalLoading = false;
        console.error('Add to cart error:', error.message);
        this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину для продления: ' + error.message);
      }
    });
  }

  onAccessTypeModalClosed(): void {
    this.showAccessTypeModal = false;
    this.accessTypeSelection = null;
  }
}

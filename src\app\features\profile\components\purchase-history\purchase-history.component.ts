import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { PurchaseService } from '../../../../core/services/purchase.service';
import { UserSummaryService } from '../../../../core/services/user-summary.service';
import { ModalService } from '../../../../core/services/modal.service';
import { Purchase, PurchaseFilters, AccessTypeSelection } from '../../../../core/models/cart.model';

@Component({
  selector: 'app-purchase-history',
  standalone: false,
  templateUrl: './purchase-history.component.html',
  styleUrl: './purchase-history.component.css'
})
export class PurchaseHistoryComponent implements OnInit, OnDestroy {
  purchases: Purchase[] = [];
  loading = false;
  error = '';

  // Pagination
  totalPurchases = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  statusFilter: 'all' | 'paid' | 'pending' | 'failed' = 'all';
  sortBy = '-created_at';
  private searchSubject = new Subject<string>();
  private searchSubscription?: Subscription;

  // Payment processing
  paymentLoading: { [key: number]: boolean } = {};

  // Access type selection modal
  showAccessTypeModal = false;
  accessTypeSelection: AccessTypeSelection | null = null;

  constructor(
    private purchaseService: PurchaseService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadPurchases();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.searchSubscription = this.searchSubject.pipe(
      debounceTime(300)
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadPurchases();
    });
  }

  loadPurchases(): void {
    this.loading = true;
    this.error = '';

    const filters: PurchaseFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.statusFilter !== 'all') {
      filters.status = this.statusFilter;
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.purchaseService.getPurchases(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.purchases = response.results;
        this.totalPurchases = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить историю покупок';
        this.loading = false;
        this.modalService.error('Ошибка', this.error);
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadPurchases();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadPurchases();
  }

  nextPage(): void {
    if (this.hasNext) {
      this.currentPage++;
      this.loadPurchases();
    }
  }

  previousPage(): void {
    if (this.hasPrevious) {
      this.currentPage--;
      this.loadPurchases();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.loadPurchases();
    }
  }

  getTotalPages(): number {
    return Math.ceil(this.totalPurchases / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  payForPurchase(purchase: Purchase): void {
    if (purchase.status !== 'pending') {
      return;
    }

    // Show access type selection modal
    this.accessTypeSelection = {
      purchaseId: purchase.id,
      gameTitle: purchase.game_title,
      price: purchase.price
    };
    this.showAccessTypeModal = true;
  }

  onAccessTypeSelected(accessType: 'oneday' | 'subscription'): void {
    if (this.accessTypeSelection) {
      const purchase = this.purchases.find(p => p.id === this.accessTypeSelection!.purchaseId);
      if (purchase) {
        this.processPayment(purchase, accessType);
      }
    }
  }

  onAccessTypeModalClosed(): void {
    this.showAccessTypeModal = false;
    this.accessTypeSelection = null;
  }

  private processPayment(purchase: Purchase, accessType: 'oneday' | 'subscription'): void {
    this.paymentLoading[purchase.id] = true;

    this.purchaseService.payForPurchase(purchase.id, accessType).subscribe({
      next: (response) => {
        this.paymentLoading[purchase.id] = false;

        // Update the purchase status locally
        const purchaseIndex = this.purchases.findIndex(p => p.id === purchase.id);
        if (purchaseIndex !== -1) {
          this.purchases[purchaseIndex].status = 'paid';
        }

        // Refresh user summary to update library count
        this.userSummaryService.refreshSummary();

        const accessTypeText = accessType === 'oneday' ? 'на 1 день' : 'на 30 дней';
        this.modalService.success('Оплата завершена', `${response.detail || 'Покупка успешно оплачена'} Доступ предоставлен ${accessTypeText}.`);
      },
      error: (error) => {
        this.paymentLoading[purchase.id] = false;
        console.error('Payment error:', error.message);
        this.modalService.error('Ошибка оплаты', error.message);
      }
    });
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(numPrice);
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'paid':
        return 'Оплачено';
      case 'pending':
        return 'Ожидает оплаты';
      case 'failed':
        return 'Ошибка оплаты';
      default:
        return status;
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  isPaymentLoading(purchaseId: number): boolean {
    return this.paymentLoading[purchaseId] || false;
  }
}

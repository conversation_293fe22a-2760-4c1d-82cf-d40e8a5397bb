# User Summary API Implementation

## Overview
A convenient user summary API integration that provides quick access to cart and library counts for enhanced UX in the profile sidebar.

## API Endpoint
```
GET {{local}}/api/user/summary/
```

## Response Format
```json
{
  "cart_count": 3,
  "library_count": 12
}
```

## Features Implemented

### ✅ User Summary Service
- **Service**: `UserSummaryService` (`src/app/core/services/user-summary.service.ts`)
- **Real-time Updates**: Uses BehaviorSubject for reactive data flow
- **Error Handling**: Graceful error handling with fallback states
- **Mock Data**: Includes mock implementation for testing

### ✅ Profile Sidebar Integration
- **Library Count**: Blue badge showing number of games in library
- **Cart Count**: Green badge showing number of items in cart
- **Real-time Updates**: Automatically updates when data changes
- **Responsive Design**: Badges adapt to content and maintain readability

### ✅ Reactive Architecture
- **Observable Pattern**: Uses RxJS observables for data flow
- **Subscription Management**: Proper cleanup to prevent memory leaks
- **Automatic Refresh**: Can be triggered when cart/library changes

## Implementation Details

### Service Architecture
```typescript
export interface UserSummary {
  cart_count: number;
  library_count: number;
}

@Injectable({
  providedIn: 'root'
})
export class UserSummaryService {
  private summarySubject = new BehaviorSubject<UserSummary | null>(null);
  public summary$ = this.summarySubject.asObservable();
  
  getUserSummary(): Observable<UserSummary>
  refreshSummary(): void
  getCurrentSummary(): UserSummary | null
  clearSummary(): void
}
```

### Profile Component Integration
```typescript
export class Profile implements OnInit, OnDestroy {
  userSummary: UserSummary | null = null;
  private summarySubscription?: Subscription;

  ngOnInit(): void {
    this.loadUserSummary();
  }

  loadUserSummary(): void {
    this.summarySubscription = this.userSummaryService.summary$.subscribe(summary => {
      this.userSummary = summary;
    });
    this.userSummaryService.getUserSummary().subscribe();
  }
}
```

### UI Implementation
```html
<!-- Library Section with Count Badge -->
<div class="flex items-center justify-between">
  <span>Библиотека</span>
  <span *ngIf="userSummary?.library_count !== undefined" 
        class="bg-blue-600/80 text-white text-xs px-2 py-0.5 rounded-full">
    {{ userSummary?.library_count }}
  </span>
</div>

<!-- Cart Section with Count Badge -->
<div class="flex items-center justify-between">
  <span>Корзина</span>
  <span *ngIf="userSummary?.cart_count !== undefined" 
        class="bg-green-600/80 text-white text-xs px-2 py-0.5 rounded-full">
    {{ userSummary?.cart_count }}
  </span>
</div>
```

## Usage Instructions

### 1. Enable Real API
To switch from mock data to real API:
```typescript
// In user-summary.service.ts
private useMockData = false; // Change to false
```

### 2. Refresh Summary
To manually refresh the summary:
```typescript
this.userSummaryService.refreshSummary();
```

### 3. Clear Summary (e.g., on logout)
```typescript
this.userSummaryService.clearSummary();
```

## Benefits

### 🚀 Performance
- **Single API Call**: Gets both counts in one request
- **Cached Data**: Uses BehaviorSubject for instant access
- **Minimal Network**: Only refreshes when needed

### 🎨 User Experience
- **Visual Feedback**: Clear badges show counts at a glance
- **Real-time Updates**: Automatically reflects changes
- **Consistent Design**: Matches existing UI patterns

### 🔧 Developer Experience
- **Type Safety**: Full TypeScript support
- **Reactive**: Observable-based architecture
- **Testable**: Mock implementation included
- **Maintainable**: Clean separation of concerns

## Testing

The service includes mock data for testing:
```typescript
private getMockSummary(): Observable<UserSummary> {
  const mockSummary: UserSummary = {
    cart_count: 3,
    library_count: 12
  };
  return of(mockSummary).pipe(delay(500));
}
```

## Future Enhancements

1. **Auto-refresh**: Automatically refresh on cart/library changes
2. **Caching**: Implement intelligent caching strategies
3. **Error States**: Enhanced error handling and user feedback
4. **Loading States**: Show loading indicators during API calls
